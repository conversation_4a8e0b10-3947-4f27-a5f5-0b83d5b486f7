import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventE<PERSON>ter, OnDestroy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { FetchAllSources } from 'src/app/reducers/global-settings/global-settings.actions';
import { getAllSources, getAllSourcesLoading } from 'src/app/reducers/global-settings/global-settings.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { Title } from '@angular/platform-browser';
import { SourceService } from 'src/app/services/controllers/source.service';
import { LeadSource } from 'src/app/app.enum';
import { environment } from 'src/environments/environment';
import { ManageSourceIndexDBService } from 'src/app/services/shared/managesource.indexdb.service';

@Component({
  selector: 'manage-source',
  templateUrl: './manage-source.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ManageSourceComponent implements OnInit, OnDestroy {
  @ViewChild('sourceWarningPopup') sourceWarningPopup: TemplateRef<any>;
  readonly NON_DISABLEABLE_SOURCES: number[] = [
    0,  // Direct source
    23, // QR Code source
  ];

  private stopper: EventEmitter<void> = new EventEmitter<void>();
  s3BucketUrl = environment.s3ImageBucketURL;
  sources: any[] = [];
  filteredSources: any[] = [];
  isSourcesLoading: boolean = false;
  searchTerm: string = '';
  permissions: Set<unknown>;
  canHide: boolean = false;
  canUnhide: boolean = false;
  sourceCountData: any = null;
  currentSource: any = null;
  isToggleInProgress: boolean = false;

  constructor(
    private store: Store<AppState>,
    private router: Router,
    private headerTitle: HeaderTitleService,
    private notificationService: NotificationsService,
    private modalService: BsModalService,
    public metaTitle: Title,
    private sourceService: SourceService,
    private cdr: ChangeDetectorRef,
    public modalRef: BsModalRef,
    private manageSourceIndexDBService: ManageSourceIndexDBService
  ) { }

  ngOnInit(): void {
    this.headerTitle.setLangTitle('Global Config');
    this.metaTitle.setTitle('CRM | Global Config');

    this.store.dispatch(new FetchAllSources());
    this.store
      .select(getAllSources)
      .pipe(takeUntil(this.stopper))
      .subscribe((sources: any[]) => {
        this.sources = sources.map(source => ({
          ...source,
          isEnabled: this.NON_DISABLEABLE_SOURCES.includes(source.value) ? true : source.isEnabled,
        }))
        this.cdr.markForCheck();
      });

    this.store
      .select(getAllSourcesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.isSourcesLoading = loading;
        this.cdr.markForCheck();
      });

    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        this.permissions = new Set(permissions);
        this.canHide = permissions?.includes('Permissions.Sources.Hide');
        this.canUnhide = permissions?.includes('Permissions.Sources.UnHide');
        this.cdr.markForCheck();
      });
  }


  navigateToLead() {
    this.isToggleInProgress = false;
    this.cdr.markForCheck();
    this.router.navigate(['leads/manage-leads'], {
      queryParams: {
        Source: JSON.stringify(LeadSource[this.currentSource.value]),
        isNavigatedFromSource: true,
      },
    });
    this.modalRef.hide();
  }

  convertToDirect() {
    this.modalRef.hide();
    this.isToggleInProgress = true;
    this.cdr.markForCheck();
    if (this.currentSource) {
      const sourceValue = typeof this.currentSource.value === 'string'
        ? parseInt(this.currentSource.value, 10)
        : this.currentSource.value;
      this.sourceService.convertToDirect(sourceValue)
        .subscribe({
          next: (response) => {
            this.isToggleInProgress = false;
            if (response && response.succeeded) {
              this.notificationService.success('Source converted successfully. Processing in background, please wait..');
              this.manageSourceIndexDBService.clearCaches();
              this.store.dispatch(new FetchAllSources());
              this.cdr.markForCheck();
            } else {
              this.notificationService.error('Failed to convert source to direct. Please try again.');
              this.cdr.markForCheck();
            }
          },
          error: (error) => {
            this.isToggleInProgress = false;
            console.error('Error converting source to direct:', error);
            this.notificationService.error('Failed to convert source to direct. Please try again.');
            this.cdr.markForCheck();
          }
        });
    }
  }

  navigateToData() {
    this.isToggleInProgress = false;
    this.cdr.markForCheck();
    this.router.navigate(['data/manage-data'], {
      queryParams: {
        SourceValue: this.currentSource.value,
        isNavigatedFromSource: true,
      },
    });
    this.modalRef.hide();
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.cdr.markForCheck();
  }

  goBack(): void {
    this.router.navigate(['/global-config']);
  }

  ngOnDestroy(): void {
    this.stopper.next();
    this.stopper.complete();
  }
}
